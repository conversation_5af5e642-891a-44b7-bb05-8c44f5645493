import { IRelatedUser } from './';
import { IWorkOrderRes } from './';

export interface IInvoiceRes {
  CreatedAt: string;
  Num: number;
  Paid: boolean;
  UpdatedAt: string;
  accountName: string;
  accountNumber: string;
  address: string;
  attachments: any | null;
  business: IInvoiceBusinessRes;
  businessId: number;
  city: string;
  country: string;
  currency: string;
  data: any | null;
  discountTotal: number;
  discountType: string;
  discountValue: number;
  dueOn: string;
  email: string;
  flags: string[];
  id: number;
  location: IInvoiceLocationRes;
  locationId: number;
  name: string;
  notes: string;
  number: string;
  paidBy: string;
  paidOn: string;
  paymentMethod: string;
  paymentTerms: string;
  phone: string;
  provider: IInvoiceProviderRes;
  providerId: number;
  province: string;
  reference: string;
  sortCode: string;
  status: string;
  subTotal: number;
  tax: number;
  taxNumber: string;
  taxRate: number;
  taxType: string;
  terms: string;
  total: number;
  value: number;
  workOrder: IWorkOrderRes;
}

interface IInvoiceBusinessRes {
  Configs: any | null;
  Contacts: any | null;
  Contractors: any | null;
  CreatedAt: string;
  Customers: any | null;
  Roles: any | null;
  Subscriptions: any | null;
  UpdatedAt: string;
  UpdatedBy: number;
  User: IRelatedUser;
  accountName: string;
  accountNumber: string;
  address: string;
  background: string;
  bank: string;
  banner: string;
  categories: any | null;
  city: string;
  colour: string;
  country: string;
  currency: string;
  description: string;
  email: string;
  enabled: boolean;
  facebook: string;
  flags: string[];
  id: number;
  instagram: string;
  latlng: string;
  locale: string;
  locations: any | null;
  name: string;
  nid: string;
  paymentTerms: string;
  phone: string;
  photo: string;
  province: string;
  rating: string;
  session: string;
  signal: string;
  sortCode: string;
  style: string;
  summary: string;
  taxId: string;
  telegram: string;
  twitter: string;
  type: string;
  uid: string;
  userId: number;
  uuid: string;
  website: string;
  whatsapp: string;
  youtube: string;
  zipcode: string;
}

interface IInvoiceLocationRes {
  Business: IInvoiceBusinessRes;
  Configs: any | null;
  CreatedAt: string;
  Equipment: any | null;
  UpdatedAt: string;
  UpdatedBy: number;
  address: string;
  businessId: number;
  city: string;
  country: string;
  email: string;
  flags: string[];
  id: number;
  latlng: string;
  message: string;
  name: string;
  phone: string;
  photo: string;
  postcode: string;
  province: string;
  rank: number;
  session: string;
  signal: string;
  telegram: string;
  uid: string;
  userId: number;
  uuid: string;
  website: string;
  whatsapp: string;
}

interface IInvoiceProviderRes {
  Configs: any | null;
  Contacts: any | null;
  Contractors: any | null;
  CreatedAt: string;
  Customers: any | null;
  Roles: any | null;
  Subscriptions: any | null;
  UpdatedAt: string;
  UpdatedBy: number;
  User: IRelatedUser;
  accountName: string;
  accountNumber: string;
  address: string;
  background: string;
  bank: string;
  banner: string;
  categories: any | null;
  city: string;
  colour: string;
  country: string;
  currency: string;
  description: string;
  email: string;
  enabled: boolean;
  facebook: string;
  flags: string[] | null;
  id: number;
  instagram: string;
  latlng: string;
  locale: string;
  locations: any | null;
  name: string;
  nid: string;
  paymentTerms: string;
  phone: string;
  photo: string;
  province: string;
  rating: string;
  session: string;
  signal: string;
  sortCode: string;
  style: string;
  summary: string;
  taxId: string;
  telegram: string;
  twitter: string;
  type: string;
  uid: string;
  userId: number;
  uuid: string;
  website: string;
  whatsapp: string;
  youtube: string;
  zipcode: string;
}

export interface IBatchResponse { //TODO: changue with api response
  id: number;
  num: string;
  status: "draft" | "sent" | "unpaid" | "paid";
  invoiceCount: number;
  totalValue: number;
  createdAt: string;
  Invoices: IInvoiceRes[];
  customFields: any;
}
