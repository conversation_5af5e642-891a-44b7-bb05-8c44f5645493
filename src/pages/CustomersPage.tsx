import React, { useContext, useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';

import QRCode from 'qrcode.react'
import { ConfigContext } from "@/context/ConfigContext";
import { BusinessContext } from "@/context/BusinessContext";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  ArrowLeft, Building2, AlertCircle, ExternalLink, FileText, MapPin, Users,
  Upload, Wrench, ClipboardList, Settings, Plus, Receipt, Loader2
} from "lucide-react";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from "@/components/ui/dialog";
import { Edit, Trash2, Image } from "lucide-react";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { WorkOrderItem } from "@/components/workOrders/WorkOrderItem";
import { currencyValue } from "@/lib/utils";
import { getFromApi, uploadGivenFile, postToApi, patchToApi } from "@/lib/api";
import { useToast } from "@/components/ui/use-toast";

import MapSelector from "@/components/map_selector";

const L = window.L;

// Fix Leaflet icon issue
delete L.Icon.Default.prototype._getIconUrl;
L.Icon.Default.mergeOptions({
  iconRetinaUrl: 'https://unpkg.com/leaflet@1.7.1/dist/images/marker-icon-2x.png',
  iconUrl: 'https://unpkg.com/leaflet@1.7.1/dist/images/marker-icon.png',
  shadowUrl: 'https://unpkg.com/leaflet@1.7.1/dist/images/marker-shadow.png',
});


export function CustomersPage() {
  const navigate = useNavigate();
  const { user } = useContext(ConfigContext)

  const {
    departments,
    cities,
    businesses,
    selectedProvince,
    selectedCity,
    setProvince,
    setCity,
    setupUser
  } = useContext(BusinessContext);

  const [selectedBusiness, setSelectedBusiness] = useState(null);

  useEffect(() => {

    //console.log("App useEffect", user)

    setupUser(user)

  }, [user]);

  const handleProvinceClick = (province) => {
    setProvince(province);
  };

  const handleCityClick = (city) => {
    setCity(city);
  };

  const handleBusinessClick = (business) => {
    setSelectedBusiness(business);
  };

  const handleBackClick = () => {
    setSelectedBusiness(null);
  };

  // If a business is selected, show its details
  if (selectedBusiness) {

    //console.log("selected business", selectedBusiness)
    return <BusinessItem user={user} business={selectedBusiness} handleBackClick={handleBackClick} />;
  }

  return (
    <div className="space-y-8">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold mb-4">Customers</h1>
          <p className="text-muted-foreground mb-6">Manage your customer information here.</p>
        </div>
        <Button
          className="flex items-center gap-2"
          onClick={() => navigate('/new-customer')}
        >
          <Plus className="h-4 w-4" />
          Add Customer
        </Button>
      </div>

      {/* Province/State Cards Grid */}
      {departments && departments.length > 0 && !selectedProvince &&
        <div>
          <h2 className="text-xl font-semibold mb-4">Select a State</h2>
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
            {departments && departments.map((province) => (
              <Card
                key={province.name}
                className={`cursor-pointer transition-all hover:shadow-md ${selectedProvince === province ? 'border-primary bg-primary/5' : ''
                  }`}
                onClick={() => handleProvinceClick(province)}
              >
                <CardContent className="p-4 flex items-center justify-between">
                  <span className="font-medium">{province}</span>
                  {selectedProvince === province && (
                    <div className="h-2 w-2 rounded-full bg-primary"></div>
                  )}
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      }

      {/* City Cards Grid - Only show when a province is selected */}
      {selectedProvince && cities && cities.length > 0 && !selectedCity && (
        <div>
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-xl font-semibold">Customer Cities in {selectedProvince}</h2>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setProvince(null)}
            >
              Back to States
            </Button>
          </div>
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
            {cities.map((city) => (
              <Card
                key={city}
                className="cursor-pointer transition-all hover:shadow-md"
                onClick={() => handleCityClick(city)}
              >
                <CardContent className="p-4">
                  <span className="font-medium">{city}</span>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      )}

      {/* Display selected city's businesses */}
      {selectedCity && (
        <div>
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-xl font-semibold">Customers in {selectedCity}, {selectedProvince}</h2>
            <div className="space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setCity(null)}
              >
                Back to Cities
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => {
                  setCity(null);
                  setProvince(null);
                }}
              >
                Back to States
              </Button>
            </div>
          </div>
          <BusinessList businesses={businesses} onBusinessClick={handleBusinessClick} />
        </div>
      )}

      {/* Display selected province's businesses (when no city is selected) */}
      {selectedProvince && !selectedCity && businesses && businesses.length > 0 && (
        <div>
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-xl font-semibold">All Customers in {selectedProvince}</h2>
          </div>
          <BusinessList businesses={businesses} onBusinessClick={handleBusinessClick} />
        </div>
      )}
    </div>
  );
}

function BusinessList({ businesses, onBusinessClick }) {
  return (
    <div className="space-y-4">
      {businesses && businesses.length > 0 ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {businesses.map((business) => (
            <Card
              key={business.id || business.name}
              className="cursor-pointer transition-all hover:shadow-md"
              onClick={() => onBusinessClick(business)}
            >
              <CardContent className="p-4">
                <h3 className="font-medium">{business.name}</h3>
                <p className="text-sm text-muted-foreground">
                  {business.city}, {business.province}
                </p>
              </CardContent>
            </Card>
          ))}
        </div>
      ) : (
        <p className="text-muted-foreground">No businesses found in this area.</p>
      )}
    </div>
  );
}

function LocationList({ locations, onLocationClick }) {
  console.log("Locations to filter", locations)
  return (
    <div className="space-y-4">
      {locations && locations.length > 0 ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {locations.map((loc) => (
            <Card
              key={loc.id || loc.name}
              className="cursor-pointer transition-all hover:shadow-md"
              onClick={() => {
                //console.log("select location", loc)
                onLocationClick(loc)
              }}
            >
              <CardContent className="p-4">
                <h3 className="font-medium">{loc.name}</h3>
                <p className="text-sm text-muted-foreground">
                  {loc.address}
                </p>
                <p className="text-sm text-muted-foreground">
                  {loc.city}, {loc.province}
                </p>
              </CardContent>
            </Card>
          ))}
        </div>
      ) : (
        <p className="text-muted-foreground">No Locations for this business.</p>
      )}
    </div>
  );
}

function BusinessItem({ user, business, handleBackClick }) {

  const {
    provider,

  } = useContext(BusinessContext);

  const [selectedLocation, setSelectedLocation] = useState(null);

  let loc = selectedLocation || business.locations[0]

  // if the selected business has multiple locations show the LocationList
  if (business.locations && business.locations.length > 1 && !selectedLocation) {
    return  <div className="space-y-8">
      <div className="flex items-center gap-4">
        <h3 className="text-lg font-medium mb-4">Select a location</h3>
        </div>
        <LocationList locations={business.locations} onLocationClick={(loc) => setSelectedLocation(loc)} />
      </div>
  }

  //console.log("selected location", selectedLocation, loc);

  return (
    <div className="space-y-8">
      <div className="flex items-center gap-4">
        <Button
          variant="outline"
          size="icon"
          onClick={handleBackClick}
          className="h-8 w-8"
        >
          <ArrowLeft className="h-4 w-4" />
        </Button>
        <div>
          <h1 className="text-3xl font-bold">{business.name}</h1>
          <p className="text-muted-foreground">
            {business.city}, {business.province}
          </p>
        </div>
      </div>

      <Tabs defaultValue="info" className="w-full">
        <TabsList className="grid grid-cols-6 w-full">
          <TabsTrigger value="info" className="flex items-center gap-2">
            <Building2 className="h-4 w-4" />
            <span>Info</span>
          </TabsTrigger>
          <TabsTrigger value="location" className="flex items-center gap-2">
            <MapPin className="h-4 w-4" />
            <span>Location</span>
          </TabsTrigger>
          <TabsTrigger value="team" className="flex items-center gap-2">
            <Users className="h-4 w-4" />
            <span>Team</span>
          </TabsTrigger>
          <TabsTrigger value="equipment" className="flex items-center gap-2">
            <Wrench className="h-4 w-4" />
            <span>Equipment</span>
          </TabsTrigger>
          {/*    <TabsTrigger value="service-history" className="flex items-center gap-2">
            <ClipboardList className="h-4 w-4" />
            <span>Service History</span>
          </TabsTrigger> */}
          <TabsTrigger value="workorders" className="flex items-center gap-2">
            <FileText className="h-4 w-4" />
            <span>Work Orders</span>
          </TabsTrigger>

          <TabsTrigger value="settings" className="flex items-center gap-2">
            <Settings className="h-4 w-4" />
            <span>Settings</span>
          </TabsTrigger>
        </TabsList>

        <TabsContent value="info" className="mt-6">
          <Card>
            <CardContent className="p-6">
              <h3 className="text-lg font-medium mb-4">Business Information</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Business Name</p>
                  <p>{business.name}</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Contact Email</p>
                  <p>{business.email || 'Not provided'}</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Phone Number</p>
                  <p>{business.phone || 'Not provided'}</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Website</p>
                  <p>{business.website || 'Not provided'}</p>
                </div>
              </div>


            </CardContent>
          </Card>

          <LocationsSection user={user} business={business} provider={provider} setSelectedLocation={setSelectedLocation} />

        </TabsContent>

        <TabsContent value="location" className="mt-6">
          <Card>
            <CardContent className="p-6">
              <h3 className="text-lg font-medium mb-4">Location Details</h3>
              <div className="grid grid-cols-1 md:grid-cols-2  gap-4">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Address</p>
                  <p>{loc.address || 'Not provided'}</p>
                  <p className="text-sm font-medium text-muted-foreground my-2">City</p>
                  <p>{loc.city}</p>
                </div>

                <div>
                  <p className="text-sm font-medium text-muted-foreground">State/Province</p>
                  <p>{loc.province}</p>
                  <p className="text-sm font-medium text-muted-foreground my-2">Postal Code</p>
                  <p>{loc.postalCode || 'Not provided'}</p>
                </div>

                <div >
                  <p className="text-sm font-medium text-muted-foreground mb-3">Location QR Code</p>
                  <p className="text-sm font-medium text-muted-foreground my-2">https://openzcan.com/loc/{loc.id}</p>
                  <div className="bg-white p-2 rounded-md">
                    <p className="text-sm font-bold text-center text-black">OpenZcan.com</p>
                    <p className="text-sm font-bold text-center text-black">Scan to start</p>
                    <QRCode value={JSON.stringify(`https://openzcan.com/loc/${loc.id}`)} size={300} />
                    <p className="text-sm font-bold text-center text-black">{business.name}</p>
                    <p className="text-sm font-bold text-center text-black">{loc.address}</p>
                  </div>
                  <div className="mt-2">
                    Scan with the OpenZcan app on a mobile device to start work at this location
                  </div>
                </div>

                <div className="mb-2">
                  <MapSelector title={null}
                    position={null}
                    location={loc} onMapClick={(e) => {
                      //console.log("map clicked", typeof e, e)
                      //setLatlng(e)
                    }} />
                </div>


              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="team" className="mt-6">
          <Card>
            <CardContent className="p-6">
              <h3 className="text-lg font-medium mb-4">Team Members</h3>
              <p className="text-muted-foreground">No team members found for this business.</p>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="equipment" className="mt-6">
          <Card>
            <CardContent className="p-6">
              <div className="flex justify-between items-center mb-4">
                <h3 className="text-lg font-medium">Equipment</h3>
                <Button size="sm" className="flex items-center gap-2">
                  <Plus className="h-4 w-4" />
                  Add Equipment
                </Button>
              </div>

              {loc.Equipment && loc.Equipment.length > 0 ? (
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead className="w-[80px]">Photo</TableHead>
                      <TableHead>Name</TableHead>
                      <TableHead>Brand</TableHead>
                      <TableHead>Model</TableHead>
                      <TableHead>Code ID</TableHead>
                      <TableHead className="text-right">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {loc.Equipment.map((item) => (
                      <TableRow key={item.id}>
                        <TableCell>
                          <Avatar className="h-10 w-10">
                            <AvatarImage src={item.photoUrl} alt={item.name} />
                            <AvatarFallback>
                              <Image className="h-4 w-4 text-muted-foreground" />
                            </AvatarFallback>
                          </Avatar>
                        </TableCell>
                        <TableCell className="font-medium">{item.name}</TableCell>
                        <TableCell>{item.brand}</TableCell>
                        <TableCell>{item.model}</TableCell>
                        <TableCell>{item.codeId}</TableCell>
                        <TableCell className="text-right">
                          <div className="flex justify-end gap-2">
                            <Button variant="ghost" size="icon" className="h-8 w-8">
                              <Edit className="h-4 w-4" />
                              <span className="sr-only">Edit</span>
                            </Button>
                            <Button variant="ghost" size="icon" className="h-8 w-8 text-destructive">
                              <Trash2 className="h-4 w-4" />
                              <span className="sr-only">Delete</span>
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              ) : (
                <div className="text-center py-6 border rounded-md bg-muted/10">
                  <div className="flex justify-center mb-2">
                    <Wrench className="h-10 w-10 text-muted-foreground" />
                  </div>
                  <p className="text-muted-foreground mb-2">No equipment records found for this business.</p>
                  <Button size="sm" variant="outline" className="flex items-center gap-2 mx-auto">
                    <Plus className="h-4 w-4" />
                    Add First Equipment
                  </Button>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="service-history" className="mt-6">
          <Card>
            <CardContent className="p-6">
              <h3 className="text-lg font-medium mb-4">Service History</h3>
              <ServiceHistoryTab user={user} business={business} location={loc} provider={provider} />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="workorders" className="mt-6">
          <Card>
            <WorkOrdersTab user={user} business={business} location={loc} provider={provider} />
          </Card>
        </TabsContent>

        <TabsContent value="settings" className="mt-6">
          <Card>
            <CardContent className="p-6">
              <h3 className="text-lg font-medium mb-4">Business Settings</h3>
              <p className="text-muted-foreground">Configure business settings here.</p>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}


function ServiceHistoryTab({ user, business, location, provider }) {
  // Sample data - replace with actual data from your business object
  const workOrders = business.workOrders || [];

  useEffect(() => {
    console.log("ServiceHistoryTab useEffect", business)

    let url = `/api/v1/provider/${business.id}/${location.id}/${provider.id}/work_order_history/90`

    if (url != '') {
      // get the equipment for this location
      getFromApi(url, user, (result) => {
        console.log("ServiceHistory results", result.result)
        setRecords(result.result)
      },
        (error) => {

        })
    }

  }, [business, location,]);

  const options = {
    dateStyle: 'short',
    timeStyle: 'short',
    hour12: true,
    day: 'numeric',
    month: 'long',
    year: '2-digit',
    minute: '2-digit',
    second: '2-digit',
  }
  let dtf = new Intl.DateTimeFormat('en-US')

  return (
    <Card>
      <CardContent className="p-6">
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-lg font-medium">Service History</h3>
          <Button size="sm" className="flex items-center gap-2">
            <Plus className="h-4 w-4" />
            Add Work Order
          </Button>
        </div>

        {workOrders.length > 0 ? (
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Work Order ID</TableHead>
                <TableHead>Date</TableHead>
                <TableHead>Technician</TableHead>
                <TableHead>Hours</TableHead>
                <TableHead>Value</TableHead>
                <TableHead className="text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {workOrders.map((order) => (
                <TableRow key={order.id}>
                  <TableCell className="font-medium">#{order.id}</TableCell>
                  <TableCell>{new Date(order.date).toLocaleDateString()}</TableCell>
                  <TableCell>{order.technician}</TableCell>
                  <TableCell>{order.hours}</TableCell>
                  <TableCell>${order.value.toFixed(2)}</TableCell>
                  <TableCell className="text-right">
                    <div className="flex justify-end gap-2">
                      <Button variant="ghost" size="icon" className="h-8 w-8">
                        <Edit className="h-4 w-4" />
                        <span className="sr-only">Edit</span>
                      </Button>
                      <Button variant="ghost" size="icon" className="h-8 w-8 text-destructive">
                        <Trash2 className="h-4 w-4" />
                        <span className="sr-only">Delete</span>
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        ) : (
          <div className="text-center py-6 border rounded-md bg-muted/10">
            <div className="flex justify-center mb-2">
              <ClipboardList className="h-10 w-10 text-muted-foreground" />
            </div>
            <p className="text-muted-foreground mb-2">No service history found for this business.</p>
            <Button size="sm" variant="outline" className="flex items-center gap-2 mx-auto">
              <Plus className="h-4 w-4" />
              Create First Work Order
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  );
}

function LocationsSection({ user, business, provider, setSelectedLocation }) {
  const { toast } = useToast();

 const [isUploading, setIsUploading] = useState(false);
  const [isInfoDialogOpen, setIsInfoDialogOpen] = useState(false);
  const [importResultData, setImportResultData] = useState(null);
  const [format, setFormat] = useState('region');

  // Create a ref for the file input
  const fileInputRef = React.useRef(null);

  const handleFileUpload = (event) => {
    const file = event.target.files[0];
    if (!file) return;

    // Only accept Excel files
    if (!file.name.endsWith('.xlsx') && !file.name.endsWith('.xls')) {
      toast({
        title: "Invalid File",
        description: "Please upload an Excel file (.xlsx or .xls)",
        variant: "destructive",
      });
      return;
    }

    let ff = format

    if (file.name.includes("External")) {
      ff = 'external'
    }

    setIsUploading(true);

    if (provider && user) {
      const path = `/api/v1/location/import/${provider.id}/${business.id}/${ff}`;

      uploadGivenFile("POST", path, user, {}, 'locations', file,
        () => { /* onProgress */ },
        () => { /* onLoad */ },
        (response) => {
          console.log("response:", response)
          if (response.result) {
            let addedCount = response.result.addedCount
            let errorCount = response.result.errorCount
            let locations = response.result.locations

            // remove locations already in the business.locations
            locations = locations.filter(l => !business.locations.find(x => x.id == l.id))

            business.locations = [...business.locations, ...locations];

            // If there are errors, show the error dialog
            if (errorCount > 0) {
              setImportResultData({
                addedCount: response.result.addedCount,
                errorCount: response.result.errorCount, 
              });
              setIsInfoDialogOpen(true);
            }
            toast({
              title: "Upload Successful",
              description: `${addedCount} locations imported successfully`,
            });
          } else {
            throw new Error(response.message || "Upload failed");
          }
          setIsUploading(false);
          // Reset the file input
          if (fileInputRef.current) {
            fileInputRef.current.value = "";
          }
        },
        (error) => {
          console.error("Error uploading locations:", error);
          toast({
            title: "Upload Failed",
            description: error.message || "Could not upload locations",
            variant: "destructive",
          });
          setIsUploading(false);
          // Reset the file input
          if (fileInputRef.current) {
            fileInputRef.current.value = "";
          }
        }

      );
    }
  };

  return (
    <div>
      <h3 className="text-lg font-medium mb-4">Locations</h3>
        <div className="flex justify-end items-right mb-2">

            <div className="flex gap-2">
              <input
                type="file"
                ref={fileInputRef}
                onChange={handleFileUpload}
                accept=".xlsx,.xls"
                className="hidden"
                id="excel-upload"
              />
              <Button
                variant="outline"
                className="flex items-center gap-2"
                onClick={() => fileInputRef.current?.click()}
                disabled={isUploading}
              >
                <Upload className="h-4 w-4" />
                {isUploading ? "Uploading..." : "Import Locations"}
              </Button>
 

            </div>
          </div>

      <LocationList locations={business.locations} onLocationClick={(loc) => setSelectedLocation(loc)} />

      {/* Result info Dialog */}
      <Dialog open={isInfoDialogOpen} onOpenChange={setIsInfoDialogOpen}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <AlertCircle className="h-5 w-5 text-amber-500" />
              Batch Creation Results
            </DialogTitle>
          </DialogHeader>

          {importResultData && (
            <div className="space-y-4">
              <div className="grid grid-cols-3 gap-4">
                <div className="bg-green-50 p-3 rounded-md border border-green-200">
                  <p className="text-sm font-medium text-green-800">Imported Locations</p> 
                </div>

                 <div className="bg-green-50 p-3 rounded-md border border-green-200">
                  <p className="text-2xl font-bold text-green-700">{importResultData.addedCount}</p>
                  <p className="text-sm font-medium text-green-800">Locations Added</p>
                </div>

                <div className="bg-red-50 p-3 rounded-md border border-red-200">
                  <p className="text-sm font-medium text-red-800">Errors</p>
                  <p className="text-2xl font-bold text-red-700">{importResultData.errorCount}</p>
                </div>

              </div>
 
            </div>
          )}

          <DialogFooter>
            <Button onClick={() => setIsInfoDialogOpen(false)}>Close</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}


function WorkOrdersTab({ user, business, location, provider }) {
  const { toast } = useToast();
  const [workOrders, setWorkOrders] = useState([]);
  const [selectedWorkOrder, setSelectedWorkOrder] = useState(null);
  const [isUploading, setIsUploading] = useState(false);
  const [isErrorDialogOpen, setIsErrorDialogOpen] = useState(false);
  const [isInfoDialogOpen, setIsInfoDialogOpen] = useState(false);
  const [errorData, setErrorData] = useState(null);
  const [isCreatingBatch, setIsCreatingBatch] = useState(false);
  const [batchResultData, setBatchResultData] = useState(null);

  // Create a ref for the file input
  const fileInputRef = React.useRef(null);

  useEffect(() => {
    if (provider) {
      fetchWorkOrders();
    }
  }, [provider, user]);

  const fetchWorkOrders = () => {
    if (provider) {
      let url = `/api/v1/provider/${provider.id}/work_orders/${business.id}/90`;
      getFromApi(url, user, (response) => {
        console.log("WorkOrdersPage useEffect", response.result)
        let workorders = response.result || [];
        setWorkOrders(workorders.sort((a, b) => a.id - b.id));
      }, (error) => {
        console.error("Error fetching work orders:", error);
      });
    }
  };

  const handleFileUpload = (event) => {
    const file = event.target.files[0];
    if (!file) return;

    // Only accept Excel files
    if (!file.name.endsWith('.xlsx') && !file.name.endsWith('.xls')) {
      toast({
        title: "Invalid File",
        description: "Please upload an Excel file (.xlsx or .xls)",
        variant: "destructive",
      });
      return;
    }

    setIsUploading(true);

    if (provider && user) {
      const path = `/api/v1/provider/${provider.id}/import/${business.id}/clickup`;

      uploadGivenFile("POST", path, user, {}, 'work_orders', file,
        () => { /* onProgress */ },
        () => { /* onLoad */ },
        (response) => {
          console.log("response:", response)
          if (response.result) {
            let addedCount = response.result.addedCount
            let errorCount = response.result.errorCount

            // If there are errors, show the error dialog
            if (errorCount > 0) {
              setErrorData({
                addedCount: response.result.addedCount,
                errorCount: response.result.errorCount,
                url: response.result.url
              });
              setIsErrorDialogOpen(true);
            }

            // Refresh the work orders list
            let fetchUrl = `/api/v1/provider/${provider.id}/work_orders/${business.id}/90`;
            getFromApi(fetchUrl, user, (response) => {
              let workorders = response.result || [];
              setWorkOrders(workorders.sort((a, b) => a.id - b.id));
              toast({
                title: "Upload Successful",
                description: `${response.count || 'Multiple'} work orders imported successfully`,
              });
            }, (error) => {
              console.error("Error fetching work orders:", error);
            });
          } else {
            throw new Error(response.message || "Upload failed");
          }
          setIsUploading(false);
          // Reset the file input
          if (fileInputRef.current) {
            fileInputRef.current.value = "";
          }
        },
        (error) => {
          console.error("Error uploading work orders:", error);
          toast({
            title: "Upload Failed",
            description: error.message || "Could not upload work orders",
            variant: "destructive",
          });
          setIsUploading(false);
          // Reset the file input
          if (fileInputRef.current) {
            fileInputRef.current.value = "";
          }
        }

      );
    }
  };

  const createInvoiceBatchFromCompletedWorkOrders = async () => {
    if (!provider || !user) {
      toast({
        title: "Error",
        description: "Provider or user information is missing",
        variant: "destructive",
      });
      return;
    }

    // Get the IDs of all completed work orders that don't have an invoice
    const completedWorkOrders = workOrders.filter(wo =>
      wo.status?.toLowerCase() === 'completed' && !wo.invoiceId
    );

    if (completedWorkOrders.length === 0) {
      toast({
        title: "No Completed Work Orders",
        description: "There are no completed work orders available to create invoices from.",
        variant: "destructive",
      });
      return;
    }

    setIsCreatingBatch(true);

    const url = `/api/v1/invoice/batch/${provider.id}/from_work_orders`
    const workOrderIds = completedWorkOrders.map(wo => wo.id);
    console.log("url:", url)
    console.log("workOrderIds:", workOrderIds)
    postToApi(url, user, workOrderIds,
      (response) => {
        console.log("response:", response.result);
        let batch = response.result;
        fetchWorkOrders();
        setIsCreatingBatch(false);
        toast({
          title: "Batch Creation Successful",
          description: `${batch.Invoices.length || 'Multiple'} invoices created successfully`,
        });
        batch.url = `/api/v1/invoice/batch/${batch.id}/export/xlsx`;
        setBatchResultData(batch);
        setIsInfoDialogOpen(true);
      },
      (error) => {
        console.error("Error creating invoice batch:", error);
        setIsCreatingBatch(false);
        toast({
          title: "Batch Creation Failed",
          description: "Could not create invoice batch from completed work orders",
          variant: "destructive",
        });
      }
    );

  };

  return (
    <div>
      <Card>
        <CardContent className="p-6">
          <h3 className="text-lg font-medium mb-4">Work Orders</h3>
          <div className="flex justify-end items-right mb-2">

            <div className="flex gap-2">
              <input
                type="file"
                ref={fileInputRef}
                onChange={handleFileUpload}
                accept=".xlsx,.xls"
                className="hidden"
                id="excel-upload"
              />
              <Button
                variant="outline"
                className="flex items-center gap-2"
                onClick={() => fileInputRef.current?.click()}
                disabled={isUploading}
              >
                <Upload className="h-4 w-4" />
                {isUploading ? "Uploading..." : "Import ClickUp Export"}
              </Button>

              <Button
                className="flex items-center gap-2"
                onClick={createInvoiceBatchFromCompletedWorkOrders}
                disabled={workOrders.filter(wo => wo.status?.toLowerCase() === 'completed').length === 0}
              >
                {isCreatingBatch ? (
                  <>
                    <Loader2 className="h-4 w-4 animate-spin" />
                    Creating Batch...
                  </>
                ) : (
                  <>
                    <Receipt className="h-4 w-4" />
                    Create Invoice Batch from Completed Work Orders
                  </>
                )}
              </Button>

            </div>
          </div>
          {workOrders && workOrders.length > 0 ? (
            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>ID</TableHead>
                    <TableHead>Customer</TableHead>
                    <TableHead>Location</TableHead>
                    <TableHead>Date</TableHead>
                    <TableHead>Description</TableHead>
                    <TableHead>Team</TableHead>
                    <TableHead>Service Records</TableHead>
                    <TableHead>Invoice</TableHead>
                    <TableHead>Status</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {workOrders.map((workOrder) => (
                    <WorkOrderItem
                      key={workOrder.id}
                      workOrder={workOrder}
                      onSelect={() => setSelectedWorkOrder(workOrder)}
                    />
                  ))}
                </TableBody>
              </Table>
            </div>
          ) : (
            <p className="text-muted-foreground">No work orders found for this business.</p>
          )}

        </CardContent>
      </Card>


      {/* Error Dialog */}
      <Dialog open={isErrorDialogOpen} onOpenChange={setIsErrorDialogOpen}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <AlertCircle className="h-5 w-5 text-amber-500" />
              Upload Results
            </DialogTitle>
          </DialogHeader>

          {errorData && (
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="bg-green-50 p-3 rounded-md border border-green-200">
                  <p className="text-sm font-medium text-green-800">Successfully Added</p>
                  <p className="text-2xl font-bold text-green-700">{errorData.addedCount}</p>
                </div>
                <div className="bg-red-50 p-3 rounded-md border border-red-200">
                  <p className="text-sm font-medium text-red-800">Errors</p>
                  <p className="text-2xl font-bold text-red-700">{errorData.errorCount}</p>
                </div>
              </div>

              <div className="bg-muted p-3 rounded-md">
                <p className="text-sm font-medium mb-2">Error Details</p>
                <div className="flex items-center gap-2">
                  <ExternalLink className="h-4 w-4 text-muted-foreground" />
                  <a
                    href={errorData.url}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-sm text-blue-600 hover:underline"
                  >
                    View detailed error report
                  </a>
                </div>
              </div>
            </div>
          )}

          <DialogFooter>
            <Button onClick={() => setIsErrorDialogOpen(false)}>Close</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Result info Dialog */}
      <Dialog open={isInfoDialogOpen} onOpenChange={setIsInfoDialogOpen}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <AlertCircle className="h-5 w-5 text-amber-500" />
              Batch Creation Results
            </DialogTitle>
          </DialogHeader>

          {batchResultData && (
            <div className="space-y-4">
              <div className="grid grid-cols-3 gap-4">
                <div className="bg-green-50 p-3 rounded-md border border-green-200">
                  <p className="text-sm font-medium text-green-800">Created Invoice Batch</p>
                  <p className="text-2xl font-bold text-green-700"># {batchResultData.num}</p>
                </div>

                 <div className="bg-green-50 p-3 rounded-md border border-green-200">
                  <p className="text-2xl font-bold text-green-700">{batchResultData.Invoices.length}</p>
                  <p className="text-sm font-medium text-green-800">Invoices Added</p>
                </div>


                 <div className="bg-green-50 p-3 rounded-md border border-green-200">
                  <p className="text-2xl font-bold text-green-700">{currencyValue(batchResultData.totalValue)}</p>
                  <p className="text-sm font-medium text-green-800">Total Value</p>
                </div>

              </div>

              <div className="bg-muted p-3 rounded-md">
                <p className="text-sm font-medium mb-2">Export Download</p>
                <div className="flex items-center gap-2">
                  <ExternalLink className="h-4 w-4 text-muted-foreground" />
                  <Button
                    variant="link"
                    className="text-sm text-blue-600 hover:underline p-0 h-auto"
                    onClick={() => window.open(batchResultData.url, '_blank')}
                  >
                    Download your invoice batch
                  </Button>
                </div>
              </div>
            </div>
          )}

          <DialogFooter>
            <Button onClick={() => setIsInfoDialogOpen(false)}>Close</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
